import { createMachine, assign } from "xstate";

// Define the context type for better type safety
interface CardCreationContext {
  isAutoBuy: boolean;
  isVerified: boolean;
  subscriptionsStatus: boolean;
  subscriptionOnlyExperiment: boolean;
  cardType: string | null;
  cardTariff: string | null;
  selectedBin: string | null;
  cardForIssue: any | null;
  promoCodeData: any | null;
  resultCard: any | null;
  numSteps: number;
  step: number;
}

// Define initial context
const initialContext: CardCreationContext = {
  isAutoBuy: false,
  isVerified: false,
  subscriptionsStatus: false,
  subscriptionOnlyExperiment: false,
  cardType: null,
  cardTariff: null,
  selectedBin: null,
  cardForIssue: null,
  promoCodeData: null,
  resultCard: null,
  numSteps: 0,
  step: 1,
};

export const machine = createMachine({
  context: initialContext, // Initial state data for the machine
  id: "create-card-machine", // Unique identifier for this state machine
  initial: "initialization", // Starting state
  states: {
    initialization: { // Initial state to set up the machine
      invoke: {
        id: "init", // Identifier for this invocation
        input: {}, // No input parameters needed
        onDone: [ // Array of possible transitions when initialization completes
          {
            target: "verifyStep", // Go to verification step
            actions: assign({ // Update context with initialization results
              isAutoBuy: (_, event) => event.output.isAutoBuy, // Whether auto-buy is enabled
              isVerified: (_, event) => event.output.isVerified, // Whether user is verified
              subscriptionsStatus: (_, event) =>
                event.output.subscriptionsStatus, // User's subscription status
              subscriptionOnlyExperiment: (_, event) =>
                event.output.subscriptionOnlyExperiment, // Experiment flag
              cardType: (_, event) => event.output.cardType, // Type of card
              numSteps: 1, // Total steps in flow
              step: 1, // Current step number
            }),
            guard: ({ event }) => !event.output.isVerified, // Only take this path if user is not verified
          },
          {
            target: "selectCardTariffStep", // Go to tariff selection
            actions: assign({
              isAutoBuy: (_, event) => event.output.isAutoBuy,
              isVerified: (_, event) => event.output.isVerified,
              subscriptionsStatus: (_, event) =>
                event.output.subscriptionsStatus,
              subscriptionOnlyExperiment: (_, event) =>
                event.output.subscriptionOnlyExperiment,
              cardType: (_, event) => event.output.cardType,
              numSteps: 1,
              step: 1,
            }),
            guard: ({ event }) => !event.output.isVerified, // Only take this path if user is not verified
          },
          {
            target: "selectBinStep", // Go to BIN selection step
            actions: assign({
              isAutoBuy: (_, event) => event.output.isAutoBuy,
              isVerified: (_, event) => event.output.isVerified,
              subscriptionsStatus: (_, event) =>
                event.output.subscriptionsStatus,
              subscriptionOnlyExperiment: (_, event) =>
                event.output.subscriptionOnlyExperiment,
              cardType: (_, event) => event.output.cardType,
              numSteps: 1,
              step: 1,
            }),
            guard: ({ event }) => !event.output.isVerified, // Only take this path if user is not verified
          },
          {
            target: "ultimaWithoutSubscriptionCardIssueStep", // Go to ultima card issue step
            actions: assign({
              isAutoBuy: (_, event) => event.output.isAutoBuy,
              isVerified: (_, event) => event.output.isVerified,
              subscriptionsStatus: (_, event) =>
                event.output.subscriptionsStatus,
              subscriptionOnlyExperiment: (_, event) =>
                event.output.subscriptionOnlyExperiment,
              cardType: (_, event) => event.output.cardType,
              numSteps: 1,
              step: 1,
            }),
            guard: ({ event }) => !event.output.isVerified, // Only take this path if user is not verified
          },
          {
            target: "selectCardTypeStep", // Default path - go to card type selection
            actions: assign({
              isAutoBuy: (_, event) => event.output.isAutoBuy,
              isVerified: (_, event) => event.output.isVerified,
              subscriptionsStatus: (_, event) =>
                event.output.subscriptionsStatus,
              subscriptionOnlyExperiment: (_, event) =>
                event.output.subscriptionOnlyExperiment,
              cardType: (_, event) => event.output.cardType,
              numSteps: 1,
              step: 1,
            }),
            // No guard - this is the default transition if no other guards match
          },
        ],
        src: "initialize", // Reference to the service that performs initialization
      },
    },
    verifyStep: { // State for user verification
      on: {
        VERIFICATION_COMPLETED: [ // When verification is completed
          {
            target: "ultimaWithoutSubscriptionCardIssueStep", // Go to ultima card issue
            actions: assign({
              isVerified: true, // Mark user as verified
              cardTariff: "ultima", // Set card tariff to ultima
              step: (context) => (context.step += 1), // Increment step counter
            }),
            guard: ({ context }) =>
              !context.subscriptionsStatus && // Only if user has no subscription
              !!context.subscriptionOnlyExperiment, // And experiment flag is true
          },
          {
            target: "selectBinStep", // Go to BIN selection
            actions: assign({
              isVerified: true,
              cardTariff: "ultima",
              step: (context) => (context.step += 1),
            }),
            guard: ({ context }) =>
              !context.subscriptionsStatus &&
              !!context.subscriptionOnlyExperiment,
          },
          {
            target: "selectCardTariffStep", // Go to tariff selection
            actions: assign({
              isVerified: true,
              cardTariff: "ultima",
              step: (context) => (context.step += 1),
            }),
            guard: ({ context }) =>
              !context.subscriptionsStatus &&
              !!context.subscriptionOnlyExperiment,
          },
          {
            target: "selectCardTypeStep", // Go to card type selection
            actions: assign({
              isVerified: true,
              cardTariff: "ultima",
              step: (context) => (context.step += 1),
            }),
            guard: ({ context }) =>
              !context.subscriptionsStatus &&
              !!context.subscriptionOnlyExperiment,
          },
        ],
      },
    },
    selectCardTariffStep: { // State for selecting card tariff
      on: {
        SELECT_CARD_TARIFF: [ // When user selects a tariff
          {
            target: "ultimaWithoutSubscriptionCardIssueStep", // Go to ultima card issue
            actions: assign({
              cardTariff: (_, event) => event.value, // Set selected tariff
              cardType: "ultima", // Set card type to ultima
              step: (context) => (context.step += 1), // Increment step counter
            }),
            guard: ({ context, event }) => {
              return !context.subscriptionsStatus && event.value === "ultima"; // Only if no subscription and ultima selected
            },
          },
          {
            target: "selectBinStep", // Default path - go to BIN selection
            actions: assign({
              cardTariff: (_, event) => event.value,
              cardType: "ultima",
              step: (context) => (context.step += 1),
            }),
          },
        ],
      },
    },
    selectBinStep: { // State for selecting card BIN
      on: {
        SELECT_CARD_BIN: [ // When user selects a BIN
          {
            target: "defaultCardIssueStep", // Go to default card issue
            actions: assign({
              selectedBin: (_, event) => event.value, // Set selected BIN
              step: (context) => (context.step += 1), // Increment step counter
            }),
            guard: ({ context }) => context.cardType === "ultima", // Only if card type is ultima
          },
          {
            target: "defaultCardIssueStep", // Duplicate transition - possible error
            actions: assign({
              selectedBin: (_, event) => event.value,
              step: (context) => (context.step += 1),
            }),
            guard: ({ context }) => context.cardType === "ultima", // Same guard as above
          },
        ],
        BACK: { // When user goes back
          target: "selectCardTariffStep", // Return to tariff selection
          actions: assign({
            cardTariff: null, // Clear tariff selection
            step: (context) => (context.step -= 1), // Decrement step counter
          }),
        },
      },
    },
    ultimaWithoutSubscriptionCardIssueStep: { // State for issuing ultima card without subscription
      on: {
        ULTIMA_WITHOUT_SUBSCRIPTION_CARD_ISSUE: [ // When card is issued
          {
            target: "approveCardStep", // Go to card approval
            actions: assign({
              cardForIssue: (_, event) => event.value, // Set card for issue
              step: (context) => (context.step += 1), // Increment step counter
            }),
            guard: ({ context }) => !context.isAutoBuy, // Only if auto-buy is disabled
          },
          {
            target: "autoBuyPaymentStep", // Go to auto-buy payment
            actions: assign({
              cardForIssue: (_, event) => event.value,
              step: (context) => (context.step += 1),
            }),
            guard: ({ context }) => !context.isAutoBuy, // Same guard as above - possible error
          },
        ],
        SELECT_CARD_TARIFF: { // When tariff is selected
          actions: assign({ cardTariff: (_, event) => event.value }), // Update tariff only
        },
        SET_PROMO_CODE: { // When promo code is set
          actions: assign({ promoCodeData: (_, event) => event.value }), // Set promo code data
        },
      },
    },
    selectCardTypeStep: { // State for selecting card type
      on: {
        SELECT_CARD_TYPE: [ // When user selects card type
          {
            target: "ultimaWithoutSubscriptionCardIssueStep", // Go to ultima card issue
            actions: assign({
              cardType: (_, event) => event.value, // Set selected card type
              cardTariff: "ultima", // Set tariff to ultima
              numSteps: 3, // Update total steps
              step: 1, // Reset current step
            }),
            guard: ({ event, context }) =>
              !context.subscriptionsStatus && event.value === "ultima", // Only if no subscription and ultima selected
          },
          {
            target: "selectBinStep", // Go to BIN selection
            actions: assign({
              cardType: (_, event) => event.value,
              cardTariff: "ultima",
              numSteps: 3,
              step: 1,
            }),
            guard: ({ event, context }) =>
              !context.subscriptionsStatus && event.value === "ultima", // Same guard as above
          },
          {
            target: "selectCardTariffStep", // Go to tariff selection
            actions: assign({
              cardType: (_, event) => event.value,
              cardTariff: "ultima",
              numSteps: 3,
              step: 1,
            }),
            context: ({ event, context }) => // ERROR: should be "guard" not "context"
              !context.subscriptionsStatus && event.value === "ultima", // Same guard as above
          },
        ],
      },
      entry: assign({ numSteps: 0, step: 1 }), // Reset steps on entry
    },
    defaultCardIssueStep: { // State for issuing default card
      on: {
        DEFAULT_CARD_ISSUE: [ // When default card is issued
          {
            target: "approveCardStep", // Go to card approval
            actions: assign({
              cardForIssue: (_, event) => event.value, // Set card for issue
              step: (context) => (context.step += 1), // Increment step counter
            }),
            guard: ({ context }) => !context.isAutoBuy, // Only if auto-buy is disabled
          },
          {
            target: "autoBuyPaymentStep", // Go to auto-buy payment
            actions: assign({
              cardForIssue: (_, event) => event.value,
              step: (context) => (context.step += 1),
            }),
            guard: ({ context }) => !context.isAutoBuy, // Same guard as above - possible error
          },
        ],
        SET_PROMO_CODE: { // When promo code is set
          actions: assign({ promoCodeData: (_, event) => event.value }), // Set promo code data
        },
        BACK: { // When user goes back
          target: "selectBinStep", // Return to BIN selection
          actions: assign({
            selectedBin: null, // Clear BIN selection
            promoCodeData: null, // Clear promo code
            step: (context) => (context.step -= 1), // Decrement step counter
          }),
        },
      },
    },
    approveCardStep: { // State for approving card
      on: {
        SINGLE_BUY_SUCCESS: { // When single buy succeeds
          target: "successStep", // Go to success step
          actions: assign({
            resultCard: (_, event) => event.value, // Set result card
            step: (context) => (context.step += 1), // Increment step counter
          }),
        },
        MULTI_BUY_SUCCESS: { // When multi buy succeeds
          target: "successStep", // Go to success step
          actions: assign({ step: (context) => (context.step += 1) }), // Increment step counter only
        },
        BACK: { // When user goes back
          target: "defaultCardIssueStep", // Return to default card issue
          actions: assign({
            cardForIssue: null, // Clear card for issue
            promoCodeData: null, // Clear promo code
            step: (context) => (context.step -= 1), // Decrement step counter
          }),
        },
      },
    },
    autoBuyPaymentStep: { // State for auto-buy payment
      on: {
        AUTO_BUY_SUCCESS: { // When auto-buy succeeds
          target: "successStep", // Go to success step
          actions: assign({ step: (context) => (context.step += 1) }), // Increment step counter
        },
        BACK: [ // When user goes back
          {
            target: "defaultCardIssueStep", // Return to default card issue
            actions: assign({
              cardForIssue: null, // Clear card for issue
              promoCodeData: null, // Clear promo code
              step: (context) => (context.step -= 1), // Decrement step counter
            }),
            guard: ({ context }) => context.cardType === "forAdv", // Only if card type is forAdv
          },
          {
            target: "ultimaWithoutSubscriptionCardIssueStep", // Return to ultima card issue
            actions: assign({
              cardForIssue: null,
              promoCodeData: null,
              step: (context) => (context.step -= 1),
            }),
            guard: ({ context }) => context.cardType === "forAdv", // Same guard as above - both can't be true
          },
        ],
      },
    },
    successStep: { // Final success state
      entry: assign({ numSteps: 0, step: 1 }), // Reset steps on entry
    },
  },
}).withConfig({
  guards: {}, // No custom guards defined
  services: {
    initialize: createMachine({ // Nested state machine for initialization
      /* ... */
    }),
  },
});