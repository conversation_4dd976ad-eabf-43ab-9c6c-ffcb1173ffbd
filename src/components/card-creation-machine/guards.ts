// Guard functions for the card creation state machine
import type { CardCreationContext, CardCreationEvent } from "./types";

export const guards = {
  // User verification guards
  isUserNotVerified: ({ event }: { event: any }) => !event.output.isVerified,
  
  isUserVerified: ({ context }: { context: CardCreationContext }) => context.isVerified,
  
  // Subscription guards
  hasNoSubscription: ({ context }: { context: CardCreationContext }) => 
    !context.subscriptionsStatus,
  
  hasSubscriptionExperiment: ({ context }: { context: CardCreationContext }) => 
    !!context.subscriptionOnlyExperiment,
  
  hasNoSubscriptionWithExperiment: ({ context }: { context: CardCreationContext }) => 
    !context.subscriptionsStatus && !!context.subscriptionOnlyExperiment,
  
  // Card type guards
  isUltimaCard: ({ context }: { context: CardCreationContext }) => 
    context.cardType === "ultima",
  
  isForAdvCard: ({ context }: { context: CardCreationContext }) => 
    context.cardType === "forAdv",
  
  // Event-based guards
  isUltimaSelected: ({ event }: { event: CardCreationEvent }) => 
    "value" in event && event.value === "ultima",
  
  isUltimaWithNoSubscription: ({ context, event }: { 
    context: CardCreationContext; 
    event: CardCreationEvent 
  }) => 
    !context.subscriptionsStatus && "value" in event && event.value === "ultima",
  
  // Auto-buy guards
  isAutoBuyDisabled: ({ context }: { context: CardCreationContext }) => 
    !context.isAutoBuy,
  
  isAutoBuyEnabled: ({ context }: { context: CardCreationContext }) => 
    context.isAutoBuy,
};

// Helper function to combine guards
export const combineGuards = {
  and: (...guardFns: Array<(params: any) => boolean>) => 
    (params: any) => guardFns.every(guard => guard(params)),
  
  or: (...guardFns: Array<(params: any) => boolean>) => 
    (params: any) => guardFns.some(guard => guard(params)),
  
  not: (guardFn: (params: any) => boolean) => 
    (params: any) => !guardFn(params),
};
