// State definitions for the card creation state machine
import { guards } from "./guards";
import { actions } from "./actions";

// Helper function to create transitions with common patterns
const createTransition = (target: string, actionKey: string, guardKey?: string) => ({
  target,
  actions: actions[actionKey as keyof typeof actions],
  ...(guard<PERSON><PERSON> && { guard: guards[guard<PERSON><PERSON> as keyof typeof guards] }),
});

// Helper function to create multiple similar transitions
const createTransitions = (configs: Array<{
  target: string;
  actionKey: string;
  guardKey?: string;
}>) => configs.map(config => createTransition(config.target, config.actionKey, config.guardKey));

export const states = {
  initialization: {
    invoke: {
      id: "init",
      input: {},
      onDone: createTransitions([
        {
          target: "verifyStep",
          actionKey: "initializeWithDefaults",
          guardKey: "isUserNotVerified",
        },
        {
          target: "selectCardTariffStep",
          actionKey: "initializeWithDefaults",
          guardKey: "isUserNotVerified",
        },
        {
          target: "selectBinStep",
          actionKey: "initializeWithDefaults",
          guard<PERSON>ey: "isUserNotVerified",
        },
        {
          target: "ultimaWithoutSubscriptionCardIssueStep",
          actionKey: "initializeWithDefaults",
          guardKey: "isUserNotVerified",
        },
        {
          target: "selectCardTypeStep",
          actionKey: "initializeWithDefaults",
        },
      ]),
      src: "initialize",
    },
  },

  verifyStep: {
    on: {
      VERIFICATION_COMPLETED: createTransitions([
        {
          target: "ultimaWithoutSubscriptionCardIssueStep",
          actionKey: "completeVerification",
          guardKey: "hasNoSubscriptionWithExperiment",
        },
        {
          target: "selectBinStep",
          actionKey: "completeVerification",
          guardKey: "hasNoSubscriptionWithExperiment",
        },
        {
          target: "selectCardTariffStep",
          actionKey: "completeVerification",
          guardKey: "hasNoSubscriptionWithExperiment",
        },
        {
          target: "selectCardTypeStep",
          actionKey: "completeVerification",
          guardKey: "hasNoSubscriptionWithExperiment",
        },
      ]),
    },
  },

  selectCardTariffStep: {
    on: {
      SELECT_CARD_TARIFF: [
        createTransition(
          "ultimaWithoutSubscriptionCardIssueStep",
          "selectCardTariffAsUltima",
          "isUltimaWithNoSubscription"
        ),
        createTransition("selectBinStep", "selectCardTariff"),
      ],
    },
  },

  selectBinStep: {
    on: {
      SELECT_CARD_BIN: [
        createTransition("defaultCardIssueStep", "selectBin", "isUltimaCard"),
        // Note: Removed duplicate transition from original
      ],
      BACK: createTransition("selectCardTariffStep", "goBackFromBin"),
    },
  },

  ultimaWithoutSubscriptionCardIssueStep: {
    on: {
      ULTIMA_WITHOUT_SUBSCRIPTION_CARD_ISSUE: [
        createTransition("approveCardStep", "issueCard", "isAutoBuyDisabled"),
        createTransition("autoBuyPaymentStep", "issueCard", "isAutoBuyEnabled"),
      ],
      SELECT_CARD_TARIFF: {
        actions: actions.selectCardTariff,
      },
      SET_PROMO_CODE: {
        actions: actions.setPromoCode,
      },
    },
  },

  selectCardTypeStep: {
    entry: actions.resetSteps,
    on: {
      SELECT_CARD_TYPE: createTransitions([
        {
          target: "ultimaWithoutSubscriptionCardIssueStep",
          actionKey: "selectCardType",
          guardKey: "isUltimaWithNoSubscription",
        },
        {
          target: "selectBinStep",
          actionKey: "selectCardType",
          guardKey: "isUltimaWithNoSubscription",
        },
        {
          target: "selectCardTariffStep",
          actionKey: "selectCardType",
          guardKey: "isUltimaWithNoSubscription",
        },
      ]),
    },
  },

  defaultCardIssueStep: {
    on: {
      DEFAULT_CARD_ISSUE: [
        createTransition("approveCardStep", "issueCard", "isAutoBuyDisabled"),
        createTransition("autoBuyPaymentStep", "issueCard", "isAutoBuyEnabled"),
      ],
      SET_PROMO_CODE: {
        actions: actions.setPromoCode,
      },
      BACK: createTransition("selectBinStep", "goBackFromApproval"),
    },
  },

  approveCardStep: {
    on: {
      SINGLE_BUY_SUCCESS: createTransition("successStep", "completeSuccess"),
      MULTI_BUY_SUCCESS: createTransition("successStep", "incrementStep"),
      BACK: createTransition("defaultCardIssueStep", "goBackFromApproval"),
    },
  },

  autoBuyPaymentStep: {
    on: {
      AUTO_BUY_SUCCESS: createTransition("successStep", "incrementStep"),
      BACK: [
        createTransition("defaultCardIssueStep", "goBackFromPayment", "isForAdvCard"),
        createTransition("ultimaWithoutSubscriptionCardIssueStep", "goBackFromPayment", "isForAdvCard"),
      ],
    },
  },

  successStep: {
    entry: actions.resetSteps,
  },
};
