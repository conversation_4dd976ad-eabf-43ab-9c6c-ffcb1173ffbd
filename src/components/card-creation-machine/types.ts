// Type definitions for the card creation state machine

export interface CardCreationContext {
  // User state
  isAutoBuy: boolean;
  isVerified: boolean;
  subscriptionsStatus: boolean;
  subscriptionOnlyExperiment: boolean;
  
  // Card configuration
  cardType: CardType | null;
  cardTariff: CardTariff | null;
  selectedBin: string | null;
  
  // Process state
  cardForIssue: CardForIssue | null;
  promoCodeData: PromoCodeData | null;
  resultCard: ResultCard | null;
  
  // Flow control
  numSteps: number;
  step: number;
}

export type CardType = "ultima" | "forAdv" | "standard";
export type CardTariff = "ultima" | "premium" | "basic";

export interface CardForIssue {
  id: string;
  type: CardType;
  tariff: CardTariff;
  bin?: string;
}

export interface PromoCodeData {
  code: string;
  discount: number;
  isValid: boolean;
}

export interface ResultCard {
  id: string;
  number: string;
  type: CardType;
  tariff: CardTariff;
}

export interface InitializationOutput {
  isAutoBuy: boolean;
  isVerified: boolean;
  subscriptionsStatus: boolean;
  subscriptionOnlyExperiment: boolean;
  cardType: CardType;
}

// Event types
export type CardCreationEvent =
  | { type: "VERIFICATION_COMPLETED" }
  | { type: "SELECT_CARD_TARIFF"; value: CardTariff }
  | { type: "SELECT_CARD_BIN"; value: string }
  | { type: "SELECT_CARD_TYPE"; value: CardType }
  | { type: "ULTIMA_WITHOUT_SUBSCRIPTION_CARD_ISSUE"; value: CardForIssue }
  | { type: "DEFAULT_CARD_ISSUE"; value: CardForIssue }
  | { type: "SET_PROMO_CODE"; value: PromoCodeData }
  | { type: "SINGLE_BUY_SUCCESS"; value: ResultCard }
  | { type: "MULTI_BUY_SUCCESS" }
  | { type: "AUTO_BUY_SUCCESS" }
  | { type: "BACK" };

// State names for better type safety
export type CardCreationState =
  | "initialization"
  | "verifyStep"
  | "selectCardTariffStep"
  | "selectBinStep"
  | "ultimaWithoutSubscriptionCardIssueStep"
  | "selectCardTypeStep"
  | "defaultCardIssueStep"
  | "approveCardStep"
  | "autoBuyPaymentStep"
  | "successStep";
