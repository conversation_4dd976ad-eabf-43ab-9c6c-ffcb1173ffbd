// Action functions for the card creation state machine
import { assign } from "xstate";
import type { CardCreationContext, InitializationOutput } from "./types";

// Common action creators to reduce duplication
export const createInitializationActions = (additionalActions: Record<string, any> = {}) => 
  assign({
    isAutoBuy: ({ event }: { event: { output: InitializationOutput } }) => event.output.isAutoBuy,
    isVerified: ({ event }: { event: { output: InitializationOutput } }) => event.output.isVerified,
    subscriptionsStatus: ({ event }: { event: { output: InitializationOutput } }) => 
      event.output.subscriptionsStatus,
    subscriptionOnlyExperiment: ({ event }: { event: { output: InitializationOutput } }) => 
      event.output.subscriptionOnlyExperiment,
    cardType: ({ event }: { event: { output: InitializationOutput } }) => event.output.cardType,
    numSteps: 1,
    step: 1,
    ...additionalActions,
  });

export const createVerificationActions = (target: string) => 
  assign({
    isVerified: true,
    cardTariff: "ultima" as const,
    step: ({ context }: { context: CardCreationContext }) => context.step + 1,
  });

export const createCardSelectionActions = (cardType?: string) => 
  assign({
    cardTariff: ({ event }: { event: any }) => event.value,
    ...(cardType && { cardType }),
    step: ({ context }: { context: CardCreationContext }) => context.step + 1,
  });

export const createCardTypeSelectionActions = () => 
  assign({
    cardType: ({ event }: { event: any }) => event.value,
    cardTariff: "ultima" as const,
    numSteps: 3,
    step: 1,
  });

export const createBinSelectionActions = () => 
  assign({
    selectedBin: ({ event }: { event: any }) => event.value,
    step: ({ context }: { context: CardCreationContext }) => context.step + 1,
  });

export const createCardIssueActions = () => 
  assign({
    cardForIssue: ({ event }: { event: any }) => event.value,
    step: ({ context }: { context: CardCreationContext }) => context.step + 1,
  });

export const createSuccessActions = () => 
  assign({
    resultCard: ({ event }: { event: any }) => event.value,
    step: ({ context }: { context: CardCreationContext }) => context.step + 1,
  });

export const createBackActions = (clearFields: string[] = []) => {
  const clearActions = clearFields.reduce((acc, field) => {
    acc[field] = null;
    return acc;
  }, {} as Record<string, null>);

  return assign({
    ...clearActions,
    step: ({ context }: { context: CardCreationContext }) => context.step - 1,
  });
};

export const createPromoCodeActions = () => 
  assign({
    promoCodeData: ({ event }: { event: any }) => event.value,
  });

export const createStepIncrementAction = () => 
  assign({
    step: ({ context }: { context: CardCreationContext }) => context.step + 1,
  });

export const createStepResetActions = () => 
  assign({
    numSteps: 0,
    step: 1,
  });

// Specific action combinations for common patterns
export const actions = {
  initializeWithDefaults: createInitializationActions(),
  
  completeVerification: createVerificationActions(""),
  
  selectCardTariff: createCardSelectionActions(),
  
  selectCardTariffAsUltima: createCardSelectionActions("ultima"),
  
  selectCardType: createCardTypeSelectionActions(),
  
  selectBin: createBinSelectionActions(),
  
  issueCard: createCardIssueActions(),
  
  completeSuccess: createSuccessActions(),
  
  goBackFromBin: createBackActions(["selectedBin", "promoCodeData"]),
  
  goBackFromApproval: createBackActions(["cardForIssue", "promoCodeData"]),
  
  goBackFromPayment: createBackActions(["cardForIssue", "promoCodeData"]),
  
  setPromoCode: createPromoCodeActions(),
  
  incrementStep: createStepIncrementAction(),
  
  resetSteps: createStepResetActions(),
};
