// Main entry point for the card creation state machine
import { createMachine } from "xstate";
import type { CardCreationContext, CardCreationEvent } from "./types";
import { guards } from "./guards";
import { states } from "./states";

// Initial context
const initialContext: CardCreationContext = {
  isAutoBuy: false,
  isVerified: false,
  subscriptionsStatus: false,
  subscriptionOnlyExperiment: false,
  cardType: null,
  cardTariff: null,
  selectedBin: null,
  cardForIssue: null,
  promoCodeData: null,
  resultCard: null,
  numSteps: 0,
  step: 1,
};

// Create the state machine with proper typing
export const cardCreationMachine = createMachine(
  {
    id: "create-card-machine",
    initial: "initialization" as const,
    context: initialContext,
    states,
  },
  {
    guards,
    services: {
      initialize: createMachine({
        id: "initialization-service",
        initial: "loading",
        states: {
          loading: {
            invoke: {
              id: "fetch-initial-data",
              src: async () => {
                // Simulate API call to fetch initial data
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                // Return mock data - replace with actual API call
                return {
                  isAutoBuy: Math.random() > 0.5,
                  isVerified: Math.random() > 0.7,
                  subscriptionsStatus: Math.random() > 0.6,
                  subscriptionOnlyExperiment: Math.random() > 0.5,
                  cardType: "ultima" as const,
                };
              },
              onDone: {
                target: "success",
              },
              onError: {
                target: "failure",
              },
            },
          },
          success: {
            type: "final",
          },
          failure: {
            on: {
              RETRY: "loading",
            },
          },
        },
      }),
    },
  }
);

// Export types for external use
export type { CardCreationContext, CardCreationEvent } from "./types";
export { guards } from "./guards";
export { actions } from "./actions";

// Helper functions for common operations
export const cardCreationHelpers = {
  // Check if user can proceed to next step
  canProceedToNextStep: (context: CardCreationContext, currentState: string): boolean => {
    switch (currentState) {
      case "selectCardTariffStep":
        return !!context.cardTariff;
      case "selectBinStep":
        return !!context.selectedBin;
      case "selectCardTypeStep":
        return !!context.cardType;
      default:
        return true;
    }
  },

  // Get progress percentage
  getProgressPercentage: (context: CardCreationContext): number => {
    if (context.numSteps === 0) return 0;
    return Math.round((context.step / context.numSteps) * 100);
  },

  // Get user-friendly state name
  getStateName: (state: string): string => {
    const stateNames: Record<string, string> = {
      initialization: "Initializing",
      verifyStep: "Verification",
      selectCardTariffStep: "Select Card Plan",
      selectBinStep: "Select Card Network",
      ultimaWithoutSubscriptionCardIssueStep: "Card Configuration",
      selectCardTypeStep: "Select Card Type",
      defaultCardIssueStep: "Card Setup",
      approveCardStep: "Review & Approve",
      autoBuyPaymentStep: "Processing Payment",
      successStep: "Complete",
    };
    return stateNames[state] || state;
  },

  // Validate context for specific states
  validateContext: (context: CardCreationContext, state: string): string[] => {
    const errors: string[] = [];
    
    switch (state) {
      case "selectBinStep":
        if (!context.cardTariff) errors.push("Card tariff must be selected");
        break;
      case "defaultCardIssueStep":
        if (!context.selectedBin) errors.push("Card network must be selected");
        break;
      case "approveCardStep":
        if (!context.cardForIssue) errors.push("Card configuration is incomplete");
        break;
    }
    
    return errors;
  },
};
